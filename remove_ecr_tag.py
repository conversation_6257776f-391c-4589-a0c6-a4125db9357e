#!/usr/bin/env python3
"""
Simple script to remove a specified tag from all ECR repositories
using boto3 and machine IAM role authentication.
"""

import boto3
import sys
from botocore.exceptions import ClientError

def remove_tag_from_all_repos(tag_to_remove, region='us-gov-west-1', repository_filter=None, dry_run=False):
    """
    Remove a specified tag from all ECR repositories.
    
    Args:
        tag_to_remove: The tag to remove from all repositories
        region: AWS region (default: us-gov-west-1)
        repository_filter: Optional filter to only process repositories matching this pattern
        dry_run: If True, show what would be done without making changes
    """
    
    # Initialize ECR client
    ecr_client = boto3.client('ecr', region_name=region)
    
    try:
        print(f"Getting list of ECR repositories in {region}...")
        
        # Get all repositories
        repositories = []
        paginator = ecr_client.get_paginator('describe_repositories')
        
        for page in paginator.paginate():
            for repo in page['repositories']:
                repositories.append(repo['repositoryName'])
        
        if not repositories:
            print("No ECR repositories found.")
            return
        
        # Filter repositories if specified
        if repository_filter:
            repositories = [repo for repo in repositories if repository_filter in repo]
            print(f"Filtered to {len(repositories)} repositories matching '{repository_filter}'")
        
        print(f"Found {len(repositories)} repositories to process")
        
        if dry_run:
            print(f"\nDRY RUN - Would remove tag '{tag_to_remove}' from:")
        else:
            print(f"\nRemoving tag '{tag_to_remove}' from repositories...")
        
        successful_deletions = 0
        failed_deletions = 0
        skipped_repos = 0
        
        for repo_name in repositories:
            try:
                # Check if the tag exists in this repository
                try:
                    response = ecr_client.describe_images(
                        repositoryName=repo_name,
                        imageIds=[{'imageTag': tag_to_remove}]
                    )
                    images = response.get('imageDetails', [])
                    
                    if not images:
                        print(f"  {repo_name}: tag '{tag_to_remove}' not found, skipping")
                        skipped_repos += 1
                        continue
                        
                except ClientError as e:
                    if e.response['Error']['Code'] == 'ImageNotFoundException':
                        print(f"  {repo_name}: tag '{tag_to_remove}' not found, skipping")
                        skipped_repos += 1
                        continue
                    else:
                        raise e
                
                if dry_run:
                    print(f"  {repo_name}: would delete tag '{tag_to_remove}'")
                    successful_deletions += 1
                else:
                    # Delete the tag
                    ecr_client.batch_delete_image(
                        repositoryName=repo_name,
                        imageIds=[{'imageTag': tag_to_remove}]
                    )
                    print(f"  ✓ {repo_name}: successfully deleted tag '{tag_to_remove}'")
                    successful_deletions += 1
                    
            except ClientError as e:
                print(f"  ✗ {repo_name}: failed to delete tag - {e}")
                failed_deletions += 1
        
        # Summary
        action = "Would delete" if dry_run else "Deleted"
        print(f"\nSummary:")
        print(f"  {action}: {successful_deletions}")
        print(f"  Failed: {failed_deletions}")
        print(f"  Skipped (tag not found): {skipped_repos}")
        
    except ClientError as e:
        error_code = e.response['Error']['Code']
        if error_code == 'InvalidClientTokenId':
            print("ERROR: Invalid AWS credentials. Ensure the machine has an IAM role with ECR permissions attached.")
        elif error_code == 'UnauthorizedOperation':
            print("ERROR: Insufficient permissions. Ensure the machine's IAM role has ECR permissions.")
        else:
            print(f"ERROR: AWS API error: {e}")
        sys.exit(1)
        
    except Exception as e:
        print(f"ERROR: {e}")
        sys.exit(1)

def main():
    """Main function with command line argument parsing."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Remove a specified tag from all ECR repositories')
    parser.add_argument('tag', help='Tag to remove from all repositories')
    parser.add_argument('--region', default='us-gov-west-1', help='AWS region (default: us-gov-west-1)')
    parser.add_argument('--filter', help='Filter repositories by name pattern')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be done without making changes')
    
    args = parser.parse_args()
    
    if not args.tag:
        print("ERROR: Tag to remove is required")
        sys.exit(1)
    
    # Confirm deletion unless it's a dry run
    if not args.dry_run:
        confirm = input(f"Are you sure you want to delete tag '{args.tag}' from all repositories? (yes/no): ")
        if confirm.lower() != 'yes':
            print("Operation cancelled.")
            return
    
    remove_tag_from_all_repos(
        tag_to_remove=args.tag,
        region=args.region,
        repository_filter=args.filter,
        dry_run=args.dry_run
    )

if __name__ == '__main__':
    main()
