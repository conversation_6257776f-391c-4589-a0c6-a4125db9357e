# ECR Image Retagging Script

This Python script loops through all ECR repositories and retags images from one tag to another (by default from "latest" to "Ax-OS-1.1.1") using machine IAM role authentication.

## Features

- Lists all ECR repositories in the specified region
- Finds images with the source tag (default: "latest")
- Creates new tags pointing to the same image (default: "Ax-OS-1.1.1")
- Uses ECR API for efficient retagging (no Docker pull/push required)
- Supports filtering repositories by name pattern
- Includes dry-run mode to preview actions
- Comprehensive logging and error handling

## Prerequisites

1. **Machine IAM role** with appropriate ECR permissions attached (EC2 instance role, ECS task role, etc.)
2. **Python 3.6+** installed
3. **Required AWS IAM permissions** for the machine's IAM role:
   - `ecr:DescribeRepositories`
   - `ecr:DescribeImages`
   - `ecr:BatchGetImage`
   - `ecr:PutImage`
   - `sts:GetCallerIdentity`

## Installation

1. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

### Basic Usage

Retag all "latest" images to "Ax-OS-1.1.1":
```bash
python retag_ecr_images.py
```

### Advanced Usage

Specify custom source and target tags:
```bash
python retag_ecr_images.py --source-tag latest --target-tag Ax-OS-1.1.1
```

Specify AWS region:
```bash
python retag_ecr_images.py --region us-west-2
```

Filter repositories by name pattern:
```bash
python retag_ecr_images.py --filter myapp
```

Dry run to see what would be done:
```bash
python retag_ecr_images.py --dry-run
```

Specify AWS account ID explicitly:
```bash
python retag_ecr_images.py --registry-id ************
```

Combine options:
```bash
python retag_ecr_images.py --region us-gov-east-1 --filter webapp --source-tag v1.0 --target-tag Ax-OS-1.1.1 --dry-run
```

Run credential diagnostics:
```bash
python retag_ecr_images.py --diagnose
```

### Command Line Options

- `--region`: AWS region for ECR repositories (default: us-gov-east-1)
- `--registry-id`: AWS account ID (auto-detected if not provided)
- `--filter`: Filter repositories by name pattern
- `--source-tag`: Source tag to retag from (default: latest)
- `--target-tag`: Target tag to retag to (default: Ax-OS-1.1.1)
- `--dry-run`: Show what would be done without making changes
- `--diagnose`: Run AWS credential diagnostics and exit

## How It Works

1. **Repository Discovery**: Lists all ECR repositories in the specified region
2. **Image Search**: For each repository, searches for images with the source tag
3. **Manifest Retrieval**: Gets the image manifest for the source tag
4. **Retagging**: Creates a new tag pointing to the same image manifest
5. **Validation**: Handles cases where target tag already exists

## Key Benefits

- **Efficient**: Uses ECR API directly, no Docker pull/push required
- **Fast**: Retagging is instant as it just creates new pointers to existing images
- **Safe**: Doesn't modify or delete existing images, only adds new tags
- **Atomic**: Each retag operation is atomic and reversible

## Error Handling

The script handles various scenarios:
- Repositories without the source tag (skipped)
- Target tag already exists (reported as success)
- Missing permissions (detailed error messages)
- Network connectivity issues
- Invalid repository names

## Example Output

```
2024-01-15 10:30:15,123 - INFO - Initialized ECR retagger for account ************ in region us-gov-east-1
2024-01-15 10:30:15,456 - INFO - Found 10 ECR repositories
2024-01-15 10:30:15,789 - INFO - Starting ECR image retagging process: latest -> Ax-OS-1.1.1
2024-01-15 10:30:16,012 - INFO - Processing repository: myapp
2024-01-15 10:30:16,234 - INFO - Successfully retagged myapp:latest -> myapp:Ax-OS-1.1.1
2024-01-15 10:30:16,567 - INFO - Processing repository: webapp
2024-01-15 10:30:16,890 - INFO - No 'latest' tag found in repository webapp, skipping
2024-01-15 10:30:17,123 - INFO - Retagging completed: 8 successful, 0 failed, 2 skipped
```

## Security Features

- Uses machine IAM roles for authentication
- No hardcoded credentials
- Minimal required permissions
- Comprehensive audit logging

## Troubleshooting

### Common Issues

1. **Invalid AWS credentials (InvalidClientTokenId)**:
   ```
   ERROR - Invalid AWS credentials. This can happen when:
     1. No IAM role is attached to this EC2 instance/container
   ```
   Solution: Run `python retag_ecr_images.py --diagnose` and ensure IAM role is attached

2. **Insufficient permissions**:
   ```
   ERROR - Insufficient permissions. Ensure the machine's IAM role has the required ECR permissions.
   ```
   Solution: Add the required ECR permissions to the IAM role

3. **Repository not found**:
   ```
   ERROR - Failed to list ECR repositories: RepositoryNotFoundException
   ```
   Solution: Verify the AWS region and account ID

## IAM Policy Example

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "ecr:DescribeRepositories",
                "ecr:DescribeImages",
                "ecr:BatchGetImage",
                "ecr:PutImage"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "sts:GetCallerIdentity"
            ],
            "Resource": "*"
        }
    ]
}
```

## Notes

- The script creates new tags without removing existing ones
- Retagging is instantaneous as it only creates new pointers to existing image layers
- The operation is idempotent - running it multiple times is safe
- Original tags remain unchanged and functional
