#!/usr/bin/env python3
"""
Docker to ECR Sync Script

This script loops through local Docker images, checks if corresponding ECR repositories exist,
creates repositories if they don't exist, and pushes images to ECR.

Requirements:
- Machine IAM role with appropriate ECR permissions
- Docker daemon running
- boto3 library installed
"""

import subprocess
import json
import sys
import logging
from typing import List, Dict, Optional
import boto3
from botocore.exceptions import ClientError, NoCredentialsError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DockerECRSync:
    def __init__(self, aws_region: str = 'us-gov-east-1', registry_id: Optional[str] = None):
        """
        Initialize the Docker ECR Sync utility.
        
        Args:
            aws_region: AWS region for ECR repositories
            registry_id: AWS account ID (optional, will be auto-detected if not provided)
        """
        self.aws_region = aws_region
        self.registry_id = registry_id
        
        try:
            # Use default credential chain (IAM role, environment variables, etc.)
            self.ecr_client = boto3.client('ecr', region_name=aws_region)
            self.sts_client = boto3.client('sts', region_name=aws_region)

            # Get account ID if not provided
            if not self.registry_id:
                self.registry_id = self.sts_client.get_caller_identity()['Account']

            logger.info(f"Initialized ECR sync for account {self.registry_id} in region {aws_region}")
            logger.info("Using machine IAM role for AWS authentication")

        except NoCredentialsError:
            logger.error("AWS credentials not found. Ensure the machine has an IAM role with ECR permissions attached.")
            sys.exit(1)
        except ClientError as e:
            if e.response['Error']['Code'] == 'UnauthorizedOperation':
                logger.error("Insufficient permissions. Ensure the machine's IAM role has the required ECR permissions.")
            else:
                logger.error(f"AWS API error during initialization: {e}")
            sys.exit(1)
        except Exception as e:
            logger.error(f"Failed to initialize AWS clients: {e}")
            sys.exit(1)

    def get_docker_images(self) -> List[Dict[str, str]]:
        """
        Get list of local Docker images.
        
        Returns:
            List of dictionaries containing image information
        """
        try:
            result = subprocess.run(
                ['docker', 'images', '--format', 'json'],
                capture_output=True,
                text=True,
                check=True
            )
            
            images = []
            for line in result.stdout.strip().split('\n'):
                if line:
                    image_data = json.loads(line)
                    # Skip images without repository names or with <none> tags
                    if image_data['Repository'] != '<none>' and image_data['Tag'] != '<none>':
                        images.append({
                            'repository': image_data['Repository'],
                            'tag': image_data['Tag'],
                            'image_id': image_data['ID'],
                            'size': image_data['Size']
                        })
            
            logger.info(f"Found {len(images)} Docker images")
            return images
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to get Docker images: {e}")
            return []
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse Docker images JSON: {e}")
            return []

    def ecr_repository_exists(self, repository_name: str) -> bool:
        """
        Check if ECR repository exists.
        
        Args:
            repository_name: Name of the ECR repository
            
        Returns:
            True if repository exists, False otherwise
        """
        try:
            self.ecr_client.describe_repositories(repositoryNames=[repository_name])
            return True
        except ClientError as e:
            if e.response['Error']['Code'] == 'RepositoryNotFoundException':
                return False
            else:
                logger.error(f"Error checking repository {repository_name}: {e}")
                return False

    def create_ecr_repository(self, repository_name: str) -> bool:
        """
        Create ECR repository.
        
        Args:
            repository_name: Name of the ECR repository to create
            
        Returns:
            True if repository was created successfully, False otherwise
        """
        try:
            self.ecr_client.create_repository(
                repositoryName=repository_name,
                imageScanningConfiguration={'scanOnPush': True},
                encryptionConfiguration={'encryptionType': 'AES256'}
            )
            logger.info(f"Created ECR repository: {repository_name}")
            return True
            
        except ClientError as e:
            if e.response['Error']['Code'] == 'RepositoryAlreadyExistsException':
                logger.info(f"Repository {repository_name} already exists")
                return True
            else:
                logger.error(f"Failed to create repository {repository_name}: {e}")
                return False

    def get_ecr_login_token(self) -> Optional[str]:
        """
        Get ECR login token for Docker authentication.
        
        Returns:
            ECR registry URL if successful, None otherwise
        """
        try:
            response = self.ecr_client.get_authorization_token()
            token = response['authorizationData'][0]['authorizationToken']
            registry_url = response['authorizationData'][0]['proxyEndpoint']
            
            # Decode and use the token for Docker login
            import base64
            username, password = base64.b64decode(token).decode().split(':')
            
            login_result = subprocess.run(
                ['docker', 'login', '--username', username, '--password-stdin', registry_url],
                input=password,
                text=True,
                capture_output=True
            )
            
            if login_result.returncode == 0:
                logger.info("Successfully logged into ECR")
                return registry_url
            else:
                logger.error(f"Failed to login to ECR: {login_result.stderr}")
                return None
                
        except Exception as e:
            logger.error(f"Failed to get ECR login token: {e}")
            return None

    def tag_and_push_image(self, image: Dict[str, str], registry_url: str) -> bool:
        """
        Tag and push Docker image to ECR.
        
        Args:
            image: Dictionary containing image information
            registry_url: ECR registry URL
            
        Returns:
            True if successful, False otherwise
        """
        source_tag = f"{image['repository']}:{image['tag']}"
        
        # Clean repository name for ECR (remove any registry prefixes)
        clean_repo_name = image['repository'].split('/')[-1] if '/' in image['repository'] else image['repository']
        ecr_tag = f"{registry_url.replace('https://', '')}/{clean_repo_name}:{image['tag']}"
        
        try:
            # Tag the image for ECR
            tag_result = subprocess.run(
                ['docker', 'tag', source_tag, ecr_tag],
                capture_output=True,
                text=True
            )
            
            if tag_result.returncode != 0:
                logger.error(f"Failed to tag image {source_tag}: {tag_result.stderr}")
                return False
            
            logger.info(f"Tagged image: {source_tag} -> {ecr_tag}")
            
            # Push the image to ECR
            push_result = subprocess.run(
                ['docker', 'push', ecr_tag],
                capture_output=True,
                text=True
            )
            
            if push_result.returncode == 0:
                logger.info(f"Successfully pushed image: {ecr_tag}")
                return True
            else:
                logger.error(f"Failed to push image {ecr_tag}: {push_result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Error tagging/pushing image {source_tag}: {e}")
            return False

    def sync_images(self, image_filter: Optional[str] = None) -> None:
        """
        Main method to sync Docker images to ECR.
        
        Args:
            image_filter: Optional filter to only sync images matching this pattern
        """
        logger.info("Starting Docker to ECR sync process...")
        
        # Get Docker images
        images = self.get_docker_images()
        if not images:
            logger.warning("No Docker images found")
            return
        
        # Filter images if specified
        if image_filter:
            images = [img for img in images if image_filter in img['repository']]
            logger.info(f"Filtered to {len(images)} images matching '{image_filter}'")
        
        # Get ECR login
        registry_url = self.get_ecr_login_token()
        if not registry_url:
            logger.error("Failed to authenticate with ECR")
            return
        
        successful_pushes = 0
        failed_pushes = 0
        
        for image in images:
            logger.info(f"Processing image: {image['repository']}:{image['tag']}")
            
            # Clean repository name for ECR
            clean_repo_name = image['repository'].split('/')[-1] if '/' in image['repository'] else image['repository']
            
            # Check if ECR repository exists
            if not self.ecr_repository_exists(clean_repo_name):
                logger.info(f"ECR repository {clean_repo_name} does not exist, creating...")
                if not self.create_ecr_repository(clean_repo_name):
                    logger.error(f"Failed to create repository {clean_repo_name}, skipping image")
                    failed_pushes += 1
                    continue
            else:
                logger.info(f"ECR repository {clean_repo_name} already exists")
            
            # Tag and push image
            if self.tag_and_push_image(image, registry_url):
                successful_pushes += 1
            else:
                failed_pushes += 1
        
        logger.info(f"Sync completed: {successful_pushes} successful, {failed_pushes} failed")


def main():
    """Main function to run the Docker ECR sync."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Sync Docker images to AWS ECR')
    parser.add_argument('--region', default='us-east-1', help='AWS region (default: us-east-1)')
    parser.add_argument('--registry-id', help='AWS account ID (auto-detected if not provided)')
    parser.add_argument('--filter', help='Filter images by repository name pattern')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be done without actually doing it')
    
    args = parser.parse_args()
    
    if args.dry_run:
        logger.info("DRY RUN MODE - No actual changes will be made")
        # For dry run, we would just list what would be done
        sync = DockerECRSync(aws_region=args.region, registry_id=args.registry_id)
        images = sync.get_docker_images()
        if args.filter:
            images = [img for img in images if args.filter in img['repository']]
        
        print(f"\nWould process {len(images)} images:")
        for image in images:
            clean_repo_name = image['repository'].split('/')[-1] if '/' in image['repository'] else image['repository']
            exists = sync.ecr_repository_exists(clean_repo_name)
            action = "push to existing" if exists else "create repo and push to"
            print(f"  {image['repository']}:{image['tag']} -> {action} ECR repo '{clean_repo_name}'")
    else:
        sync = DockerECRSync(aws_region=args.region, registry_id=args.registry_id)
        sync.sync_images(image_filter=args.filter)


if __name__ == '__main__':
    main()
