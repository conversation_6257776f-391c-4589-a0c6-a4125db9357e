#!/usr/bin/env python3
"""
Simple script to delete all ECR repositories in us-gov-east-1 region
using machine IAM role authentication.
"""

import boto3
import sys
from botocore.exceptions import ClientError

def delete_all_ecr_repos():
    """Delete all ECR repositories in us-gov-east-1 region."""
    
    # Initialize ECR client for us-gov-east-1
    ecr_client = boto3.client('ecr', region_name='us-gov-east-1')
    
    try:
        # Get all repositories
        print("Getting list of ECR repositories...")
        repositories = []
        paginator = ecr_client.get_paginator('describe_repositories')
        
        for page in paginator.paginate():
            for repo in page['repositories']:
                repositories.append(repo['repositoryName'])
        
        if not repositories:
            print("No ECR repositories found.")
            return
        
        print(f"Found {len(repositories)} repositories:")
        for repo in repositories:
            print(f"  - {repo}")
        
        # Confirm deletion
        confirm = input(f"\nAre you sure you want to delete all {len(repositories)} repositories? (yes/no): ")
        if confirm.lower() != 'yes':
            print("Deletion cancelled.")
            return
        
        # Delete each repository
        deleted_count = 0
        failed_count = 0
        
        for repo_name in repositories:
            try:
                print(f"Deleting repository: {repo_name}")
                ecr_client.delete_repository(
                    repositoryName=repo_name,
                    force=True  # Delete even if it contains images
                )
                print(f"✓ Successfully deleted: {repo_name}")
                deleted_count += 1
                
            except ClientError as e:
                print(f"✗ Failed to delete {repo_name}: {e}")
                failed_count += 1
        
        print(f"\nDeletion completed:")
        print(f"  Successfully deleted: {deleted_count}")
        print(f"  Failed: {failed_count}")
        
    except ClientError as e:
        error_code = e.response['Error']['Code']
        if error_code == 'InvalidClientTokenId':
            print("ERROR: Invalid AWS credentials. Ensure the machine has an IAM role with ECR permissions attached.")
        elif error_code == 'UnauthorizedOperation':
            print("ERROR: Insufficient permissions. Ensure the machine's IAM role has ECR delete permissions.")
        else:
            print(f"ERROR: AWS API error: {e}")
        sys.exit(1)
        
    except Exception as e:
        print(f"ERROR: {e}")
        sys.exit(1)

if __name__ == '__main__':
    delete_all_ecr_repos()
