#!/usr/bin/env python3
"""
ECR Image Retagging Script

This script loops through all ECR repositories, finds images tagged as 'latest',
and retags them with Ax-OS Version using machine IAM role authentication.

Requirements:
- Machine IAM role with appropriate ECR permissions
- Docker daemon running
- boto3 library installed
"""

import subprocess
import json
import sys
import logging
from typing import List, Dict, Optional
import boto3
from botocore.exceptions import ClientError, NoCredentialsError




# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def diagnose_aws_credentials():
    """
    Diagnose AWS credential issues and provide helpful information.
    """
    logger.info("Diagnosing AWS credential configuration...")
    
    # Check if running on EC2
    try:
        import requests
        response = requests.get(
            'http://***************/latest/meta-data/iam/security-credentials/',
            timeout=2
        )
        if response.status_code == 200:
            role_name = response.text.strip()
            logger.info(f"Running on EC2 instance with IAM role: {role_name}")
            
            # Check role credentials
            cred_response = requests.get(
                f'http://***************/latest/meta-data/iam/security-credentials/{role_name}',
                timeout=2
            )
            if cred_response.status_code == 200:
                import json
                cred_data = json.loads(cred_response.text)
                logger.info(f"IAM role credentials found, expires at: {cred_data.get('Expiration', 'Unknown')}")
            else:
                logger.warning("Could not retrieve IAM role credentials")
        else:
            logger.info("Not running on EC2 instance or no IAM role attached")
    except Exception as e:
        logger.info(f"Could not check EC2 metadata: {e}")
    
    # Check environment variables
    import os
    if os.environ.get('AWS_ACCESS_KEY_ID'):
        logger.info("AWS_ACCESS_KEY_ID environment variable found")
    if os.environ.get('AWS_SECRET_ACCESS_KEY'):
        logger.info("AWS_SECRET_ACCESS_KEY environment variable found")
    if os.environ.get('AWS_SESSION_TOKEN'):
        logger.info("AWS_SESSION_TOKEN environment variable found")
    
    # Check AWS credentials file
    aws_creds_file = os.path.expanduser('~/.aws/credentials')
    if os.path.exists(aws_creds_file):
        logger.info(f"AWS credentials file found at: {aws_creds_file}")
    else:
        logger.info("No AWS credentials file found")


class ECRImageRetagger:
    def __init__(self, aws_region: str = 'us-gov-east-1', registry_id: Optional[str] = None):
        """
        Initialize the ECR Image Retagger utility.
        
        Args:
            aws_region: AWS region for ECR repositories
            registry_id: AWS account ID (optional, will be auto-detected if not provided)
        """
            
        self.aws_region = aws_region
        self.registry_id = registry_id
        self.new_tag = version
        self.source_tag = "latest"
        
        try:
            # Use default credential chain (IAM role, environment variables, etc.)
            self.ecr_client = boto3.client('ecr', region_name=aws_region)
            self.sts_client = boto3.client('sts', region_name=aws_region)
            
            # Get account ID if not provided
            if not self.registry_id:
                self.registry_id = self.sts_client.get_caller_identity()['Account']
                
            logger.info(f"Initialized ECR retagger for account {self.registry_id} in region {aws_region}")
            logger.info("Using machine IAM role for AWS authentication")
            
        except NoCredentialsError:
            logger.error("AWS credentials not found. Ensure the machine has an IAM role with ECR permissions attached.")
            diagnose_aws_credentials()
            sys.exit(1)
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'UnauthorizedOperation':
                logger.error("Insufficient permissions. Ensure the machine's IAM role has the required ECR permissions.")
            elif error_code == 'InvalidClientTokenId':
                logger.error("Invalid AWS credentials. This can happen when:")
                logger.error("  1. No IAM role is attached to this EC2 instance/container")
                logger.error("  2. The IAM role credentials have expired")
                logger.error("  3. Using invalid AWS access keys")
                logger.error("  4. Clock skew between local machine and AWS (check system time)")
                logger.error("Solution: Attach a valid IAM role to your EC2 instance or container")
                diagnose_aws_credentials()
            elif error_code == 'TokenRefreshRequired':
                logger.error("AWS credentials have expired. The IAM role credentials need to be refreshed.")
                diagnose_aws_credentials()
            elif error_code == 'SignatureDoesNotMatch':
                logger.error("AWS signature mismatch. Check system clock and ensure it's synchronized.")
            else:
                logger.error(f"AWS API error during initialization: {e}")
                logger.error(f"Error code: {error_code}")
                diagnose_aws_credentials()
            sys.exit(1)
        except Exception as e:
            logger.error(f"Failed to initialize AWS clients: {e}")
            sys.exit(1)

    def get_all_repositories(self) -> List[str]:
        """
        Get list of all ECR repositories.
        
        Returns:
            List of repository names
        """
        repositories = []
        try:
            paginator = self.ecr_client.get_paginator('describe_repositories')
            
            for page in paginator.paginate():
                for repo in page['repositories']:
                    repositories.append(repo['repositoryName'])
            
            logger.info(f"Found {len(repositories)} ECR repositories")
            return repositories
            
        except ClientError as e:
            logger.error(f"Failed to list ECR repositories: {e}")
            return []

    def get_images_with_tag(self, repository_name: str, tag: str) -> List[Dict]:
        """
        Get images in a repository with a specific tag.
        
        Args:
            repository_name: Name of the ECR repository
            tag: Tag to search for
            
        Returns:
            List of image details
        """
        try:
            response = self.ecr_client.describe_images(
                repositoryName=repository_name,
                imageIds=[{'imageTag': tag}]
            )
            return response.get('imageDetails', [])
            
        except ClientError as e:
            if e.response['Error']['Code'] == 'ImageNotFoundException':
                logger.debug(f"No images with tag '{tag}' found in repository {repository_name}")
                return []
            else:
                logger.error(f"Error getting images from repository {repository_name}: {e}")
                return []

    def delete_image_tag(self, repository_name: str, tag: str) -> bool:
        """
        Delete a specific tag from an ECR repository.

        Args:
            repository_name: Name of the ECR repository
            tag: Tag to delete

        Returns:
            True if successful, False otherwise
        """
        try:
            self.ecr_client.batch_delete_image(
                repositoryName=repository_name,
                imageIds=[{'imageTag': tag}]
            )
            logger.info(f"Successfully deleted tag '{tag}' from repository {repository_name}")
            return True

        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'ImageNotFoundException':
                logger.warning(f"Tag '{tag}' not found in repository {repository_name}")
                return False
            else:
                logger.error(f"Failed to delete tag '{tag}' from repository {repository_name}: {e}")
                return False
        except Exception as e:
            logger.error(f"Unexpected error deleting tag '{tag}' from repository {repository_name}: {e}")
            return False

    def retag_image(self, repository_name: str, source_tag: str, target_tag: str) -> bool:
        """
        Retag an image in ECR by creating a new tag pointing to the same image.

        Args:
            repository_name: Name of the ECR repository
            source_tag: Existing tag to copy from
            target_tag: New tag to create

        Returns:
            True if successful, False otherwise
        """
        try:
            # Get the image manifest for the source tag
            response = self.ecr_client.batch_get_image(
                repositoryName=repository_name,
                imageIds=[{'imageTag': source_tag}]
            )
            
            if not response['images']:
                logger.warning(f"No image found with tag '{source_tag}' in repository {repository_name}")
                return False
            
            image = response['images'][0]
            image_manifest = image['imageManifest']
            
            # Put the same manifest with the new tag
            self.ecr_client.put_image(
                repositoryName=repository_name,
                imageManifest=image_manifest,
                imageTag=target_tag
            )
            
            logger.info(f"Successfully retagged {repository_name}:{source_tag} -> {repository_name}:{target_tag}")
            return True
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'ImageAlreadyExistsException':
                logger.info(f"Tag '{target_tag}' already exists in repository {repository_name}")
                return True
            elif error_code == 'ImageNotFoundException':
                logger.warning(f"Image with tag '{source_tag}' not found in repository {repository_name}")
                return False
            else:
                logger.error(f"Failed to retag image in repository {repository_name}: {e}")
                return False
        except Exception as e:
            logger.error(f"Unexpected error retagging image in repository {repository_name}: {e}")
            return False

    def retag_all_latest_images(self, repository_filter: Optional[str] = None, delete_source_tag: bool = True) -> None:
        """
        Main method to retag all 'latest' images to version tag.

        Args:
            repository_filter: Optional filter to only process repositories matching this pattern
            delete_source_tag: Whether to delete the source tag after successful retagging
        """
        logger.info(f"Starting ECR image retagging process: {self.source_tag} -> {self.new_tag}")
        
        # Get all repositories
        repositories = self.get_all_repositories()
        if not repositories:
            logger.warning("No ECR repositories found")
            return
        
        # Filter repositories if specified
        if repository_filter:
            repositories = [repo for repo in repositories if repository_filter in repo]
            logger.info(f"Filtered to {len(repositories)} repositories matching '{repository_filter}'")
        
        successful_retags = 0
        failed_retags = 0
        skipped_repos = 0
        
        for repo_name in repositories:
            logger.info(f"Processing repository: {repo_name}")
            
            # Check if repository has images with 'latest' tag
            latest_images = self.get_images_with_tag(repo_name, self.source_tag)
            
            if not latest_images:
                logger.info(f"No '{self.source_tag}' tag found in repository {repo_name}, skipping")
                skipped_repos += 1
                continue
            
            # Retag the image
            if self.retag_image(repo_name, self.source_tag, self.new_tag):
                successful_retags += 1

                # Delete the source tag after successful retagging if requested
                if delete_source_tag and self.source_tag == "latest":
                    logger.info(f"Removing '{self.source_tag}' tag from repository {repo_name}")
                    if self.delete_image_tag(repo_name, self.source_tag):
                        logger.info(f"Successfully removed '{self.source_tag}' tag from {repo_name}")
                    else:
                        logger.warning(f"Failed to remove '{self.source_tag}' tag from {repo_name}")
            else:
                failed_retags += 1
        
        logger.info(f"Retagging completed: {successful_retags} successful, {failed_retags} failed, {skipped_repos} skipped")


def main():
    """Main function to run the ECR image retagging."""
    import argparse
    
    parser = argparse.ArgumentParser(description=f'Retag ECR images from latest to {version}')
    parser.add_argument('--region', default='us-gov-east-1', help='AWS region (default: us-gov-east-1)')
    parser.add_argument('--registry-id', help='AWS account ID (auto-detected if not provided)')
    parser.add_argument('--filter', help='Filter repositories by name pattern')
    parser.add_argument('--source-tag', default='latest', help='Source tag to retag from (default: latest)')
    parser.add_argument('--target-tag', default=version, help=f'Target tag to retag to (default: {version})')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be done without actually doing it')
    parser.add_argument('--keep-source-tag', action='store_true', help='Keep the source tag after retagging (default: delete source tag)')
    parser.add_argument('--diagnose', action='store_true', help='Run AWS credential diagnostics and exit')
    
    args = parser.parse_args()
    
    if args.diagnose:
        diagnose_aws_credentials()
        return
    
    retagger = ECRImageRetagger(aws_region=args.region, registry_id=args.registry_id)
    
    # Override default tags if specified
    retagger.source_tag = args.source_tag
    retagger.new_tag = args.target_tag
    
    if args.dry_run:
        logger.info("DRY RUN MODE - No actual changes will be made")
        repositories = retagger.get_all_repositories()
        
        if args.filter:
            repositories = [repo for repo in repositories if args.filter in repo]
        
        print(f"\nWould process {len(repositories)} repositories:")
        for repo_name in repositories:
            latest_images = retagger.get_images_with_tag(repo_name, retagger.source_tag)
            if latest_images:
                action = f"retag '{retagger.source_tag}' -> '{retagger.new_tag}'"
                if not args.keep_source_tag and retagger.source_tag == "latest":
                    action += f" and delete '{retagger.source_tag}'"
                print(f"  {repo_name}: {action}")
            else:
                print(f"  {repo_name}: no '{retagger.source_tag}' tag found, would skip")
    else:
        delete_source = not args.keep_source_tag
        retagger.retag_all_latest_images(repository_filter=args.filter, delete_source_tag=delete_source)


if __name__ == '__main__':
    main()
