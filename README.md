# Docker to ECR Sync Script

This Python script automatically syncs local Docker images to AWS Elastic Container Registry (ECR). It loops through your local Docker images, checks if corresponding ECR repositories exist, creates them if needed, and pushes the images.

## Features

- Lists all local Docker images
- Checks if ECR repositories exist for each image
- Creates ECR repositories automatically if they don't exist
- Authenticates with ECR using AWS credentials
- Tags and pushes images to ECR
- Supports filtering images by name pattern
- Includes dry-run mode to preview actions
- Comprehensive logging

## Prerequisites

1. **Machine IAM role** with appropriate ECR permissions attached (EC2 instance role, ECS task role, etc.)

2. **Docker daemon running** and accessible

3. **Python 3.6+** installed

4. **Required AWS IAM permissions** for the machine's IAM role:
   - `ecr:GetAuthorizationToken`
   - `ecr:CreateRepository`
   - `ecr:DescribeRepositories`
   - `ecr:BatchCheckLayerAvailability`
   - `ecr:GetDownloadUrlForLayer`
   - `ecr:BatchGetImage`
   - `ecr:PutImage`
   - `ecr:InitiateLayerUpload`
   - `ecr:UploadLayerPart`
   - `ecr:CompleteLayerUpload`


## IAM Role Setup

The script uses the AWS SDK's default credential chain, which automatically detects and uses:
- IAM roles for EC2 instances
- IAM roles for ECS tasks
- IAM roles for Lambda functions
- Environment variables (AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY)
- AWS credentials file

### Example IAM Policy

Create an IAM policy with the following permissions and attach it to your machine's role:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "ecr:GetAuthorizationToken",
                "ecr:CreateRepository",
                "ecr:DescribeRepositories",
                "ecr:BatchCheckLayerAvailability",
                "ecr:GetDownloadUrlForLayer",
                "ecr:BatchGetImage",
                "ecr:PutImage",
                "ecr:InitiateLayerUpload",
                "ecr:UploadLayerPart",
                "ecr:CompleteLayerUpload"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "sts:GetCallerIdentity"
            ],
            "Resource": "*"
        }
    ]
}
```

## Installation

1. Clone or download this repository
2. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

### Basic Usage

Sync all local Docker images to ECR:
```bash
python sync_docker_ecr.py
```

### Advanced Usage

Specify AWS region:
```bash
python sync_docker_ecr.py --region us-west-2
```

Filter images by name pattern:
```bash
python sync_docker_ecr.py --filter myapp
```

Dry run to see what would be done:
```bash
python sync_docker_ecr.py --dry-run
```

Specify AWS account ID explicitly:
```bash
python sync_docker_ecr.py --registry-id ************
```

Combine options:
```bash
python sync_docker_ecr.py --region eu-west-1 --filter webapp --dry-run
```

Run credential diagnostics:
```bash
python sync_docker_ecr.py --diagnose
```

Delete local ECR-tagged Docker images after successful push:
```bash
python sync_docker_ecr.py --delete-after-push
```

### Command Line Options

- `--region`: AWS region for ECR repositories (default: us-east-1)
- `--registry-id`: AWS account ID (auto-detected if not provided)
- `--filter`: Filter images by repository name pattern
- `--dry-run`: Show what would be done without making changes
- `--delete-after-push`: Delete local ECR-tagged Docker images after successful push
- `--diagnose`: Run AWS credential diagnostics and exit

## How It Works

1. **Discovery**: Lists all local Docker images using `docker images`
2. **Filtering**: Skips images with `<none>` repository or tag names
3. **Repository Check**: For each image, checks if an ECR repository exists
4. **Repository Creation**: Creates ECR repository if it doesn't exist
5. **Authentication**: Gets ECR login token and authenticates Docker
6. **Tagging**: Tags local image with ECR registry URL
7. **Pushing**: Pushes tagged image to ECR repository
8. **Cleanup** (optional): Deletes local ECR-tagged Docker image after successful push

## Repository Naming

The script cleans repository names for ECR compatibility:
- Removes any registry prefixes (e.g., `docker.io/myapp` becomes `myapp`)
- Uses the last part of the path as the ECR repository name

## Error Handling

The script includes comprehensive error handling for:
- Missing AWS credentials
- Docker daemon not running
- Network connectivity issues
- ECR permission errors
- Image tagging/pushing failures

## Logging

The script provides detailed logging including:
- Images being processed
- Repository creation status
- Push success/failure status
- Summary of operations

## Security Features

- ECR repositories are created with:
  - Image scanning enabled (`scanOnPush: true`)
  - AES256 encryption
- Uses temporary ECR authentication tokens
- No hardcoded credentials

## Example Output

```
2024-01-15 10:30:15,123 - INFO - Initialized ECR sync for account ************ in region us-east-1
2024-01-15 10:30:15,456 - INFO - Found 5 Docker images
2024-01-15 10:30:16,789 - INFO - Successfully logged into ECR
2024-01-15 10:30:17,012 - INFO - Processing image: myapp:latest
2024-01-15 10:30:17,234 - INFO - ECR repository myapp does not exist, creating...
2024-01-15 10:30:18,567 - INFO - Created ECR repository: myapp
2024-01-15 10:30:19,890 - INFO - Tagged image: myapp:latest -> ************.dkr.ecr.us-east-1.amazonaws.com/myapp:latest
2024-01-15 10:30:45,123 - INFO - Successfully pushed image: ************.dkr.ecr.us-east-1.amazonaws.com/myapp:latest
2024-01-15 10:30:45,456 - INFO - Sync completed: 5 successful, 0 failed
```

## Troubleshooting

### Common Issues

1. **Invalid AWS credentials (InvalidClientTokenId)**:
   ```
   ERROR - Invalid AWS credentials. This can happen when:
     1. No IAM role is attached to this EC2 instance/container
     2. The IAM role credentials have expired
     3. Using invalid AWS access keys
     4. Clock skew between local machine and AWS (check system time)
   ```
   Solution:
   - Run `python sync_docker_ecr.py --diagnose` to check credential configuration
   - Attach a valid IAM role to your EC2 instance or container
   - Ensure system clock is synchronized with NTP

2. **Machine IAM role not configured**:
   ```
   ERROR - AWS credentials not found. Ensure the machine has an IAM role with ECR permissions attached.
   ```
   Solution: Attach an IAM role with ECR permissions to your EC2 instance, ECS task, or container

2. **Docker daemon not running**:
   ```
   ERROR - Failed to get Docker images: [Errno 2] No such file or directory: 'docker'
   ```
   Solution: Start Docker daemon

3. **Insufficient ECR permissions**:
   ```
   ERROR - Failed to create repository: An error occurred (AccessDeniedException)
   ```
   Solution: Check IAM permissions listed in prerequisites

4. **Network connectivity issues**:
   ```
   ERROR - Failed to push image: dial tcp: lookup on server misbehaving
   ```
   Solution: Check internet connection and AWS service availability

## License

This script is provided as-is for educational and practical use.
