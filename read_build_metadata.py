#!/usr/bin/env python3
"""
Build Metadata Reader Script

This script reads a JSON file and extracts the "Version" value from it.
Designed to read from /axonius/cortex/shared_readonly_files/__build_metadata
"""

import json
import sys
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def read_version_from_json(file_path: str) -> str:
    """
    Read JSON file and extract the Version value.
    
    Args:
        file_path: Path to the JSON file
        
    Returns:
        Version string if found, None otherwise
    """
    try:
        # Check if file exists
        if not Path(file_path).exists():
            logger.error(f"File not found: {file_path}")
            return None
        
        # Read and parse JSON file
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        # Extract Version value (try different possible key names)
        version_keys = ['Version', 'version', 'Verson', 'verson']  # Including the typo "Verson"
        
        for key in version_keys:
            if key in data:
                version = data[key]
                logger.info(f"Found {key}: {version}")
                return version
        
        # If no version key found, list available keys
        logger.warning(f"No version key found. Available keys: {list(data.keys())}")
        return None
        
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON format in file {file_path}: {e}")
        return None
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {e}")
        return None


def main():
    """Main function to read build metadata."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Read version from build metadata JSON file')
    parser.add_argument('--file', 
                       default='/axonius/cortex/shared_readonly_files/__build_metadata',
                       help='Path to the JSON metadata file')
    parser.add_argument('--key', 
                       default='Version',
                       help='JSON key to extract (default: Version)')
    parser.add_argument('--list-keys', 
                       action='store_true',
                       help='List all available keys in the JSON file')
    
    args = parser.parse_args()
    
    try:
        # Check if file exists
        if not Path(args.file).exists():
            logger.error(f"File not found: {args.file}")
            
            # Try some common variations
            possible_paths = [
                args.file + '.json',
                args.file + '.txt',
                './build_metadata.json',
                './__build_metadata',
                './__build_metadata.json'
            ]
            
            logger.info("Trying alternative file paths...")
            for alt_path in possible_paths:
                if Path(alt_path).exists():
                    logger.info(f"Found alternative file: {alt_path}")
                    args.file = alt_path
                    break
            else:
                logger.error("No alternative files found")
                sys.exit(1)
        
        # Read and parse JSON file
        with open(args.file, 'r') as f:
            data = json.load(f)
        
        if args.list_keys:
            print("Available keys in the JSON file:")
            for key in data.keys():
                print(f"  - {key}: {data[key]}")
            return
        
        # Extract the requested key
        if args.key in data:
            value = data[args.key]
            print(f"{args.key}: {value}")
            logger.info(f"Successfully extracted {args.key}: {value}")
        else:
            logger.error(f"Key '{args.key}' not found in JSON file")
            logger.info(f"Available keys: {list(data.keys())}")
            
            # Try case-insensitive search
            key_lower = args.key.lower()
            for key in data.keys():
                if key.lower() == key_lower:
                    value = data[key]
                    print(f"{key}: {value}")
                    logger.info(f"Found case-insensitive match - {key}: {value}")
                    return
            
            sys.exit(1)
        
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON format in file {args.file}: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Error reading file {args.file}: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
