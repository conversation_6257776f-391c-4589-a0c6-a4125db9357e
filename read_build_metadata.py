#!/usr/bin/env python3
"""
Simple script to read Version from build metadata JSON file
"""

import json

# Read the JSON file
file_path = '/axonius/cortex/shared_readonly_files/__build_metadata'

try:
    with open(file_path, 'r') as f:
        data = json.load(f)

    # Try different possible key names for version
    version_keys = ['Version', 'version', 'Verson', 'verson']

    for key in version_keys:
        if key in data:
            print(data[key])
            break
    else:
        print("Version key not found")

except FileNotFoundError:
    print(f"File not found: {file_path}")
except json.JSONDecodeError:
    print("Invalid JSON format")
except Exception as e:
    print(f"Error: {e}")
